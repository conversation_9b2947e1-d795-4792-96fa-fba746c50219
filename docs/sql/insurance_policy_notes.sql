WITH
    INSURANCE_POLICY_NOTES_ AS
        (
            select  insurance_policy_id,insurance_policy_note_id,user_activity_type as activity_type
            from (
                     select insurance_policy_id,
                            insurance_policy_note_id,
                            user_activity_type,
                            row_number() over (partition by insurance_policy_id, user_activity_type order by insurance_policy_note_id desc) as last_user_activity_type_rn
                     from (
                              select  /*+ opt_param('optimizer_index_cost_adj',30) */
                                  ipn.insurance_policy_id,
                                  ipn.Insurance_policy_note_id,
                                  sw.manufacturing_context_id,
                                  case when ipn.user_activity_type_cd in ('VIEW_QUO', 'UPLOAD_QUO', 'CHG_FLW_ST') then 'OTHER'
                                       when ipn.user_activity_type_cd in ('ADD_NOTE')                             then 'NOTE'
                                      end as user_activity_type
                              from plpadmin.ins_pol_not_mv  ipn,
                                   plpadmin.search_wz       sw
                              where 1=1
                                and sw.insurance_policy_id      =  ipn.insurance_policy_id
--                                 and sw.manufacturing_context_id =  :v_manufacturing_context_id
                          )
                 )
            where last_user_activity_type_rn = 1
        )

SELECT ROWNUM RNUM,
       a.insurance_policy_id,
       a.party_id,
       a.externalSystemOriginCd,
       a.actDte,
       a.actAuthor,
       a.actTypeCde,
       a.ipAgreementNb,
       a.ipAgreementLegacyNbr,
       a.pvSynchro,
       a.sNameLine1,
       a.sNameLine2,
       a.sNameLine3,
       a.adrLine1,
       a.adrLine2,
       a.adrLine3,
       a.adrCityCd,
       a.adrProvinceCd,
       a.adrPostalCd,
       a.adrCountryCd,
       a.btLastUpdateTs,
       a.btTransactionStatusCd,
       a.ipQuotationValidityExpiryDt,
       a.ipSystemCreateTs,
       a.ipAgreementFollowUpStatusCd,
       a.followUpStatusCd,
       a.followupdte,
       a.followupauthor,
       a.followupcontactnote,
       a.sub_broker,
       a.sbNameLine1,
       a.sbNameLine2,
       a.sbNameLine3,
       a.langCommCd,
       CASE
           WHEN (SELECT COUNT(cst.party_id)
                 FROM plpadmin.consent cst
                 WHERE cst.party_id= a.party_id
                   AND cst.consent_type_cd = 'QF'
                   AND cst.consent_ind = 'N') >= 1 THEN 'N'
           ELSE 'Y'
           END AS consent,
       -- Additional fields for roadblock messages, CVI values, vehicle classes, etc.
       a.lineofbusinesscd
FROM
    (
        SELECT /*+ opt_param('optimizer_index_cost_adj',30) */
            sw.insurance_policy_id,
            pa.party_id,                                                  -- FIXED: Added missing party_id
            sw.external_system_origin_cd                                  AS externalSystemOriginCd,
            ipn.system_create_ts                                          AS actDte,
            ipn.author_u_id                                               AS actAuthor,
            ipn.user_activity_type_cd                                     AS actTypeCde,
            sw.agreement_nbr                                              AS ipAgreementNb,
            sw.agreement_legacy_nbr                                       AS ipAgreementLegacyNbr,
            CASE
                WHEN sw.combined_policy_cd = 'C'
                    THEN 'Y'
                WHEN sw.combined_policy_cd = 'M' AND sw.combined_policy_scenario_cd ='C'
                    THEN 'Y'
                ELSE 'N'
                END                                                           AS pvSynchro,
            pa.name_line_1_txt                                            AS sNameLine1,
            pa.name_line_2_txt                                            AS sNameLine2,
            pa.name_line_3_txt                                            AS sNameLine3,
            adr.address_line_1_txt                                        AS adrLine1,
            adr.address_line_2_txt                                        AS adrLine2,
            adr.address_line_3_txt                                        AS adrLine3,
            adr.city_cd                                                   AS adrCityCd,
            adr.province_cd                                               AS adrProvinceCd,
            adr.postal_cd                                                 AS adrPostalCd,
            adr.country_cd                                                AS adrCountryCd,
            bt.last_update_ts                                             AS btLastUpdateTs,
            bt.transaction_status_cd                                      AS btTransactionStatusCd,
            sw.quotation_validity_expiry_dt                               AS ipQuotationValidityExpiryDt,
            sw.ip_system_create_ts                                        AS ipSystemCreateTs,
            sw.agreement_follow_up_status_cd                              AS ipAgreementFollowUpStatusCd,
            follow.followUpStatusCd                                       AS followUpStatusCd,
            follow.followupdte                                            AS followupdte,
            follow.followupauthor                                         AS followupauthor,
            follow.followupcontactnote                                    AS followupcontactnote,
            sb.sub_broker                                                 AS sub_broker,
            sb.name_line_1_txt                                            AS sbNameLine1,
            sb.name_line_2_txt                                            AS sbNameLine2,
            sb.name_line_3_txt                                            AS sbNameLine3,
            pa.language_communication_cd                                  AS langCommCd,
            bta.business_trx_activity_cd                                  AS trxactcd,
            bta.business_trx_activity_id                                  AS business_trx_activity_id,
            pa.unstructured_name_txt                                      AS unstructuredNameTxt,
            ip.line_of_business_cd                                        AS lineOfBusinessCd
        FROM
            (
                SELECT 'ADD_NOTE'                AS followUpStatusCd,
                       system_create_ts          AS followupdte,
                       author_u_id               AS followupauthor,
                       insurance_policy_note     AS followupcontactnote,
                       i.insurance_policy_id     AS insurance_policy_id
                FROM plpadmin.ins_pol_not_mv    sipn,
                     INSURANCE_POLICY_NOTES_    i
                WHERE i.activity_type = 'NOTE'
                  AND sipn.insurance_policy_note_id = i.insurance_policy_note_id
            )
                follow,
            INSURANCE_POLICY_NOTES_                 ipnv,
            cifadmin.sub_brokers                    sb,
            plpadmin.business_trx_activity          bta,
            plpadmin.search_wz                      sw,
            plpadmin.ins_pol_not_mv                 ipn,
            plpadmin.party                          pa,
            plpadmin.business_transaction           bt,
            plpadmin.address                        adr,
            plpadmin.insurance_policy               ip
        WHERE ip.insurance_policy_id           = sw.insurance_policy_id
          AND follow.insurance_policy_id(+)    = sw.insurance_policy_id
          AND ipnv.insurance_policy_id(+)      = sw.insurance_policy_id
          AND ipnv.insurance_policy_note_id    = ipn.insurance_policy_note_id(+)
          AND sw.cif_sub_broker_id             = sb.sub_broker
          AND sw.party_id                      = pa.party_id
          AND sw.business_transaction_id       = bt.business_transaction_id
          AND pa.party_id                      = adr.party_id
          -- **CRITICAL: Quote reference number filter**
          AND sw.agreement_nbr like 'IR113408182%'
          AND bta.business_trx_activity_id = (SELECT MAX(business_trx_activity_id)
                                              FROM plpadmin.business_trx_activity
                                              WHERE sw.business_transaction_id = business_transaction_id)
--           AND sw.manufacturer_company_cd      = :t_manuf_company
          AND sb.search_province              = 'QC'
--           AND sb.company_number               = :t_sub_company_nbr
          AND nvl(ipnv.activity_type,'OTHER') = 'OTHER'
--           AND sw.manufacturing_context_id     = :v_manufacturing_context_id
          -- **CRITICAL: Broker access control (this is where the issue occurs)**
          -- When quote number >= 10 characters, v_selected_point_of_sale is NULL
          -- [v_selected_point_of_sale would be inserted here if quote < 10 chars]
          -- **CRITICAL: User web access security**
          AND ((sb.sub_broker_number,sb.company_number) in (select bwa.master_owner_cd, bwa.company_cd
                                                            from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa))
          AND ( exists( SELECT 1 FROM plpadmin.consent co WHERE sw.party_id = co.party_id AND co.consent_ind = 'Y' AND co.consent_type_cd = 'PR' )
            OR not exists( SELECT 1 FROM plpadmin.consent co WHERE sw.party_id = co.party_id AND co.consent_type_cd = 'PR' )
            )
        ORDER BY sw.ip_system_create_ts desc
    ) A
WHERE ROWNUM <= 201
ORDER BY btLastUpdateTs desc
