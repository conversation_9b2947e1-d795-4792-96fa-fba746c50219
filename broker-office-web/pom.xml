<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>intact.web.brokeroffice</groupId>
		<artifactId>broker-office-project</artifactId>
		<version>6.0.0-SNAPSHOT</version>
	</parent>

	<artifactId>broker-office-web</artifactId>
	<packaging>war</packaging>

	<name>Intact BROKER OFFICE WEB (war)</name>
	<description>Broker Office Web module (WAR) - (a.k.a WebZone)</description>

	<profiles>
		<profile>
			<id>local</id>
			<build>
				<plugins>
					<plugin>
						<!-- When building the war locally, we must copy tomcat's config to the appropriate folder -->
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-resources-plugin</artifactId>
						<executions>
							<execution>
								<id>copy-resources</id>
								<phase>validate</phase>
								<goals>
									<goal>copy-resources</goal>
								</goals>
								<configuration>
									<resources>
										<resource>
											<directory>
												${basedir}/../broker-office-local-config/src/main/tomcat
											</directory>
										</resource>
									</resources>
									<outputDirectory>
										${project.build.directory}/${project.build.finalName}/META-INF
									</outputDirectory>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<build>
		<finalName>${project.build.artifactName}</finalName>
		<!-- Plugin definitions for the build process -->
		<plugins>
			<plugin>
				<artifactId>maven-war-plugin</artifactId>
				<configuration>
					<failOnMissingWebXml>false</failOnMissingWebXml>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
							<classpathPrefix>lib/</classpathPrefix>
						</manifest>
						<manifestEntries>
							<Build-Time>${maven.build.timestamp}</Build-Time>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<properties>
		<m2eclipse.wtp.contextRoot>WebZone</m2eclipse.wtp.contextRoot>
		<myfaces.version>4.1.1</myfaces.version>
		<primefaces.version>14.0.12</primefaces.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.sun.xml.ws</groupId>
			<artifactId>jaxws-rt</artifactId>
			<version>4.0.3</version>
			<!--			<scope>provided</scope>-->
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>2.14.0</version>
		</dependency>

		<!--		Spring deps-->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>

<!--		Jackson-->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-xml</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>${jackson.version}</version>
		</dependency>

		<!--esapi-->
		<dependency>
			<groupId>org.owasp.esapi</groupId>
			<artifactId>esapi</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core-jakarta</artifactId>
			<version>${hibernate.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.bytebuddy</groupId>
					<artifactId>byte-buddy</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.atomikos</groupId>
			<artifactId>transactions-jta</artifactId>
			<classifier>jakarta</classifier>
		</dependency>
		<dependency>
			<groupId>com.atomikos</groupId>
			<artifactId>transactions-jdbc</artifactId>
			<classifier>jakarta</classifier>
		</dependency>

		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>4.0.2</version>
		</dependency>
		<dependency>
			<groupId>jakarta.annotation</groupId>
			<artifactId>jakarta.annotation-api</artifactId>
			<version>1.3.5</version>
		</dependency>
		<dependency>
			<groupId>com.sun.activation</groupId>
			<artifactId>jakarta.activation</artifactId>
			<version>2.0.1</version>
		</dependency>
		<dependency>
			<groupId>jakarta-oro</groupId>
			<artifactId>jakarta-oro</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.inject</groupId>
			<artifactId>jakarta.inject-api</artifactId>
			<version>2.0.1.MR</version>
		</dependency>
		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
			<version>6.1.0</version>
<!--			<scope>provided</scope>-->
		</dependency>
		<dependency>
			<groupId>jakarta.enterprise</groupId>
			<artifactId>jakarta.enterprise.cdi-api</artifactId>
			<version>4.1.0</version>
		</dependency>
		<dependency>
			<groupId>jakarta.xml.ws</groupId>
			<artifactId>jakarta.xml.ws-api</artifactId>
			<version>4.0.2</version>
		</dependency>

		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>jaxb-runtime</artifactId>
		</dependency>
		<dependency>
			<groupId>org.glassfish.hk2</groupId>
			<artifactId>osgi-resource-locator</artifactId>
			<version>2.4.0</version>
		</dependency>

		<dependency>
			<groupId>net.bytebuddy</groupId>
			<artifactId>byte-buddy</artifactId>
			<version>1.17.6</version>
		</dependency>

		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-validator</groupId>
			<artifactId>commons-validator</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.6.0</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<!-- requires by file upload -->
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>
		<dependency>
			<groupId>oro</groupId>
			<artifactId>oro</artifactId>
			<version>2.0.8</version>
			<scope>runtime</scope>
		</dependency>

		<!--		MyFaces-->
		<dependency>
			<groupId>org.apache.myfaces.core</groupId>
			<artifactId>myfaces-api</artifactId>
			<version>${myfaces.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.myfaces.core</groupId>
			<artifactId>myfaces-impl</artifactId>
			<version>${myfaces.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.myfaces.core</groupId>
					<artifactId>myfaces-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--Primefaces-->

		<dependency>
			<groupId>org.primefaces</groupId>
			<artifactId>primefaces</artifactId>
			<classifier>jakarta</classifier>
			<version>${primefaces.version}</version>
		</dependency>
		<dependency>
			<groupId>org.primefaces.extensions</groupId>
			<artifactId>primefaces-extensions</artifactId>
			<classifier>jakarta</classifier>
			<version>${primefaces.version}</version>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-el</groupId>
			<artifactId>commons-el</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<version>3.30.2-GA</version>
		</dependency>
		<dependency>
			<groupId>com.github.detro</groupId>
			<artifactId>phantomjsdriver</artifactId>
			<version>1.2.0</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.seleniumhq.selenium</groupId>
					<artifactId>selenium-remote-driver</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-java</artifactId>
			<version>${selenium.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>xml-apis</artifactId>
					<groupId>xml-apis</groupId>
				</exclusion>
				<exclusion>
					<groupId>xerces</groupId>
					<artifactId>xercesImpl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>xalan</groupId>
					<artifactId>xalan</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.eclipse.jetty</groupId>
					<artifactId>jetty-io</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-server</artifactId>
			<version>${selenium.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>5.18.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
        </exclusion>
      </exclusions>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-reflect</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-support</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-util</artifactId>
			<version>1.19</version>
			<exclusions>
				<exclusion>
					<groupId>xml-apis</groupId>
					<artifactId>xml-apis</artifactId>
				</exclusion>
				<exclusion>
					<groupId>xerces</groupId>
					<artifactId>xercesImpl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>xalan</groupId>
					<artifactId>xalan</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- -->
		<!-- INTACT DEPENDENCIES -->
		<!-- -->
		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-template-jexl</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-logging-slf4j</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-sanitization-api</artifactId>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-sanitization-intact</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>intact.commons.capi</groupId>
			<artifactId>common-api-java-services</artifactId>
			<version>${capi.version}</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>intact.commons.capi</groupId>
			<artifactId>common-api-il-services</artifactId>
			<version>${capi.version}</version>
			<exclusions>
				<exclusion>
					<groupId>net.sf.ehcache</groupId>
					<artifactId>ehcache-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>intact.commons.service.plp</groupId>
			<artifactId>plp-api</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-entitymanager</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>intact.commons.service.plp</groupId>
			<artifactId>plp-services</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-entitymanager</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<!-- to have access to cif-services-beans.xml -->
			<groupId>intact.commons.service.cif</groupId>
			<artifactId>cif-services</artifactId>
			<version>${cif.version}</version>
			<scope>runtime</scope>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>intact.commons.service.cif</groupId>
			<artifactId>cif-service-api</artifactId>
			<version>${cif.version}</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>intact.commons.service.brm</groupId>
			<artifactId>brm-service</artifactId>
			<version>${brm.version}</version>
			<scope>runtime</scope>
			<exclusions>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-entitymanager</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>intact.web.brokeroffice</groupId>
			<artifactId>broker-office-business</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>jdom</artifactId>
					<groupId>org.jdom</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-api-ldap</artifactId>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-multithreading-std</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-cache-ehcache</artifactId>
			<version>2.2.0.7</version>
			<scope>runtime</scope>
			<exclusions>
				<exclusion>
					<groupId>net.sf.ehcache</groupId>
					<artifactId>ehcache</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>net.sf.ehcache.internal</groupId>
			<artifactId>ehcache-core</artifactId>
			<version>2.11.1.3.11</version>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-comparison-api</artifactId>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-comparison-intact</artifactId>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-string-apache</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-junit-jupiter</artifactId>
			<version>5.18.0</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

</project>
