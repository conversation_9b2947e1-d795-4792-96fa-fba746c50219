<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>intact.web.brokeroffice</groupId>
		<artifactId>broker-office-project</artifactId>
		<version>6.0.0-SNAPSHOT</version>
	</parent>

	<artifactId>broker-office-business</artifactId>
	<packaging>jar</packaging>

	<name>Intact BROKER OFFICE BUSINESS</name>
	<description>Broker Office Business- (a.k.a WebZone)</description>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<addClasspath>false</addClasspath>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
						</manifest>
						<manifestEntries>
							<Build-Time>${maven.build.timestamp}</Build-Time>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<dependencies>
		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>4.0.2</version>
		</dependency>

		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>jaxb-runtime</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
		</dependency>

		<!--		Spring deps-->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.ldap</groupId>
			<artifactId>spring-ldap</artifactId>
		</dependency>

		<dependency>
			<groupId>org.owasp.esapi</groupId>
			<artifactId>esapi</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-beanutils</artifactId>
					<groupId>commons-beanutils</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--			SLF4J-->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.oracle.ojdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>19.3.0.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.openjpa</groupId>
			<artifactId>openjpa</artifactId>
			<version>3.2.2</version>
			<scope>provided</scope>
		</dependency>

		<!-- JSON-simple used to read json files for dialer broker structure -->
		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
			<version>1.1.1</version>
			<exclusions>
				<exclusion>
					<groupId>junit</groupId>
					<artifactId>junit</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>org.easymock</groupId>
			<artifactId>easymock</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.easymock</groupId>
			<artifactId>easymockclassextension</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>5.18.0</version>
			<scope>test</scope>
		</dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>5.18.0</version>
      <scope>test</scope>
    </dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
        </exclusion>
      </exclusions>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4-common</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-reflect</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-support</artifactId>
			<scope>test</scope>
		</dependency>

		<!--                       -->
		<!--  INTACT DEPENDENCIES  -->
		<!--                       -->

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-string-api</artifactId>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-logging-api</artifactId>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-cache-api</artifactId>
		</dependency>

		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-template-api</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.inject</groupId>
			<artifactId>jakarta.inject-api</artifactId>
			<version>2.0.1.MR</version>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>intact.business</groupId>-->
<!--			<artifactId>business-broker-api</artifactId>-->
<!--		</dependency>-->

<!--		<dependency>-->
<!--			<groupId>intact.business</groupId>-->
<!--			<artifactId>business-broker-common</artifactId>-->
<!--		</dependency>-->

		<dependency>
			<groupId>intact.business</groupId>
			<artifactId>business-broker-plp</artifactId>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-core</artifactId>
				</exclusion>
        <exclusion>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
        </exclusion>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jdom</artifactId>
					<groupId>org.jdom</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>intact.business</groupId>
			<artifactId>business-broker-plt</artifactId>
		</dependency>

		<dependency>
			<groupId>intact.commons.plt</groupId>
			<artifactId>plt-service-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
		</dependency>
		<dependency>
			<groupId>intact.commons.service.cif</groupId>
			<artifactId>cif-service-api</artifactId>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>intact.commons.service.plp</groupId>
			<artifactId>plp-api</artifactId>
		</dependency>
		<dependency>
			<groupId>intact.commons.service.brm</groupId>
			<artifactId>brm-api</artifactId>
			<version>${brm.version}</version>
		</dependency>
		<dependency>
			<groupId>intact.commons.service.brm</groupId>
			<artifactId>brm-service</artifactId>
			<version>${brm.version}</version>
		</dependency>
		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-api-ldap</artifactId>
		</dependency>
		<dependency>
			<groupId>intact.tools</groupId>
			<artifactId>tools-multithreading-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>

	</dependencies>
</project>
